# API配置
BACKEND_CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:8080,http://localhost:5173

# 数据库配置
DATABASE_URL=sqlite+aiosqlite:///./data/yue_platform.db

# OpenAI配置
OPENAI_API_KEY=sk-06649e0573904c039c8624c675998472
# 如果使用API代理，可以设置以下变量
OPENAI_API_BASE=https://api.deepseek.com/v1

# 模型配置
LLM_MODEL=deepseek-chat

# 应用配置
PROJECT_NAME=YUE智能体平台
VERSION=0.1.0
DEBUG=false

# 服务器配置
HOST=0.0.0.0
PORT=8000

# JWT配置
SECRET_KEY=yue-platform-secret-key-2023
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 客服配置
CUSTOMER_SERVICE_NAME=小yue
MAX_CONVERSATION_HISTORY=50

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log